{"folders": [{"name": "RT-Thread MicroPython", "path": "Micro Python Program"}], "settings": {"MicroPython.executeButton": [{"text": "▶", "tooltip": "运行", "alignment": "left", "command": "extension.executeFile", "priority": 3.5}], "MicroPython.syncButton": [{"text": "$(sync)", "tooltip": "同步", "alignment": "left", "command": "extension.execute", "priority": 4}], "python.autoComplete.extraPaths": ["c:/Users/<USER>/.vscode/extensions/rt-thread.rt-thread-micropython-1.0.11/microExamples/code-completion"], "files.associations": {".mpyproject.json": "jsonc"}, "json.schemas": []}}